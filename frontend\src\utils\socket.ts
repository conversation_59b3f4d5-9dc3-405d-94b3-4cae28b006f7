
import { io, Socket } from "socket.io-client";
import type { User } from "../types";

const BASE_URL = "http://localhost:8081";

let socket: Socket | null = null;

export const getSocket = () => {
    if(socket === null)return;
    return socket;
}

export const connectSocket = (user:User) => {

    if(!user || socket) return;
    socket = io(BASE_URL, {
        query: {
            userId: user._id
        }   
    });
    socket.connect()
    
}

export const disconnectSocket = () => {
    if(socket)
        socket.disconnect()
}